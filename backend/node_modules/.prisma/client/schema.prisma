// This is your Prisma schema file,
// learn more about it in the docs: https://pris.lyd/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 角色模型
model Role {
  id          Int          @id @default(autoincrement())
  name        String       @unique // admin, manager, agent, user
  description String?
  permissions Permission[]
  users       User[]       @relation("UserRoles")
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

// 权限模型
model Permission {
  id          Int      @id @default(autoincrement())
  code        String   @unique // user:read, user:write, etc.
  name        String // 权限名称
  description String? // 权限描述
  module      String // 所属模块
  roles       Role[] // 多对多关系
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 更新User模型，添加transactions关系
model User {
  id                  Int                     @id @default(autoincrement())
  username            String                  @unique
  email               String                  @unique
  password            String
  roles               Role[]                  @relation("UserRoles") // 多对多关系
  status              String                  @default("active") // active, inactive, blocked
  phone               String?
  balance             Float                   @default(0)
  totalConsumption    Float                   @default(0)
  createdAt           DateTime                @default(now())
  updatedAt           DateTime                @updatedAt
  lastLoginAt         DateTime?
  lastLoginIp         String?
  documents           Document[]
  payments            Payment[]
  orders              Order[]
  agent               Agent?
  loginLogs           LoginLog[]
  operationLogs       OperationLog[]
  apiLogs             ApiLog[]
  errorLogs           ErrorLog[]
  operatedSensitive   SensitiveOperationLog[] @relation("Operator")
  approvedSensitive   SensitiveOperationLog[] @relation("Approver")
  resolvedAlerts      Alert[]                 @relation("AlertResolver")
  transactions        Transaction[] // 添加交易记录关系
  workOrders          WorkOrder[] // 客户工单
  expertProfile       Expert? // 专家档案
  uploadedAttachments WorkOrderAttachment[] // 上传的附件
  comments            WorkOrderComment[] // 评论记录
  refunds             Refund[] // 退款记录
}

model Document {
  id               Int     @id @default(autoincrement())
  userId           Int
  user             User    @relation(fields: [userId], references: [id])
  title            String
  content          String
  optimizedContent String?

  // 统一文档历史字段
  fileName     String? // 显示文件名（如：学术论文+优化版.docx）
  originalName String? // 原始文件名
  serviceType  String    @default("ai_optimization") // ai_optimization, expert_optimization
  status       String    @default("completed") // processing, completed, cancelled
  filePath     String? // 文件存储路径
  downloadUrl  String? // 下载链接
  expiresAt    DateTime? // 过期时间（一个月后）
  orderId      Int? // 关联的订单ID（专家服务）

  workOrders WorkOrder[] // 关联工单
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
}

// 支付相关模型
model Order {
  id            Int        @id @default(autoincrement())
  userId        Int
  user          User       @relation(fields: [userId], references: [id])
  amount        Float // 订单金额
  status        String // pending, paid, cancelled, processing, completed
  paymentMethod String? // alipay, wechat, balance
  serviceType   String     @default("ai_optimization") // 服务类型: ai_optimization, expert_optimization
  description   String? // 订单描述
  workOrder     WorkOrder? // 关联工单（专家服务）

  // 专家服务相关字段
  processingStatus String? // processing, completed (专家处理状态)
  resultFilePath   String? // 处理结果文件路径
  resultFileName   String? // 处理结果文件名
  completedAt      DateTime? // 完成时间

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  refunds   Refund[] // 退款记录
}

model Payment {
  id                   Int       @id @default(autoincrement())
  userId               Int
  user                 User      @relation(fields: [userId], references: [id])
  orderId              String    @unique
  realOrderId          Int? // 真实的订单ID
  amount               Float
  type                 String // recharge, consume
  status               String // pending, success, failed
  outTradeNo           String? // 商户订单号
  tradeNo              String? // 支付宝交易号
  paymentTime          DateTime? // 支付完成时间
  verificationMethod   String? // notify, query - 验证方式
  payment_method       String? // 支付方式: alipay, wechat, fuiou
  fuiou_order_no       String? // 富有支付订单号
  fuiou_transaction_id String? // 富有支付交易号
  paid_at              DateTime? // 支付完成时间（富有支付）
  description          String? // 订单描述
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  refunds              Refund[] // 退款记录
}

// 代理管理相关模型
model Agent {
  id              Int           @id @default(autoincrement())
  userId          Int           @unique // 关联到User模型
  user            User          @relation(fields: [userId], references: [id])
  agentName       String
  level           String // 代理级别
  commission      Float // 佣金比例
  totalSales      Float         @default(0)
  totalCommission Float         @default(0)
  status          String        @default("active") // active, inactive
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  transactions    Transaction[] // 添加交易记录关系
}

// 系统设置模型
model Setting {
  type  String
  key   String
  value String // SQLite不支持@db.Text，使用默认的String类型

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@id([type, key])
  @@index([type])
}

// 内容管理模型
model Agreement {
  id        Int      @id @default(autoincrement())
  title     String
  content   String
  version   String   @unique // 版本控制
  type      String // 协议类型: user_registration, service_terms, privacy_policy
  isActive  Boolean  @default(false) // 是否当前生效
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Announcement {
  id          Int       @id @default(autoincrement())
  title       String
  content     String
  publishDate DateTime  @default(now())
  expireDate  DateTime?
  isPublished Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// API 配置模型
model ApiConfig {
  id          Int      @id @default(autoincrement())
  key         String   @unique // 配置项的键，例如 'google_api_key', 'alipay_app_id'
  value       String // 配置项的值
  type        String // 配置项的类型，例如 'google', 'alipay', 'wechat_pay'
  description String? // 描述
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 营销工具模型
model Coupon {
  id             Int       @id @default(autoincrement())
  code           String    @unique
  type           String // 例如: 'percentage', 'fixed_amount'
  value          Float
  minOrderAmount Float? // 最低订单金额
  usageLimit     Int       @default(1) // 使用次数限制
  usedCount      Int       @default(0)
  expiresAt      DateTime?
  isActive       Boolean   @default(true)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
}

model Promotion {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  startDate   DateTime
  endDate     DateTime
  type        String // 例如: 'discount', 'buy_one_get_one'
  details     String? // JSON字符串，存储促销活动的具体规则
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 安全中心模型
model IpBlacklist {
  id        Int      @id @default(autoincrement())
  ipAddress String   @unique
  reason    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 敏感操作日志
model SensitiveOperationLog {
  id         Int      @id @default(autoincrement())
  userId     Int
  user       User     @relation(name: "Operator", fields: [userId], references: [id])
  operation  String // 操作描述
  details    String? // 操作详情
  status     String // pending, approved, rejected
  approverId Int?
  approver   User?    @relation(name: "Approver", fields: [approverId], references: [id])
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

// 登录日志
model LoginLog {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  ip        String
  device    String?
  browser   String?
  status    String // success, failed
  createdAt DateTime @default(now())
}

// 操作日志
model OperationLog {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  action    String
  target    String?
  details   String?
  ip        String
  createdAt DateTime @default(now())
}

// API调用日志
model ApiLog {
  id          Int      @id @default(autoincrement())
  endpoint    String // API端点
  method      String // HTTP方法
  userId      Int? // 调用用户ID，可以为空（未登录用户）
  user        User?    @relation(fields: [userId], references: [id])
  status      Int // HTTP状态码
  duration    Int // 响应时间（毫秒）
  requestBody String? // 请求体
  error       String? // 错误信息
  createdAt   DateTime @default(now())

  @@index([endpoint])
  @@index([userId])
  @@index([createdAt])
}

// 系统性能指标
model SystemMetric {
  id          Int      @id @default(autoincrement())
  cpuUsage    Float // CPU使用率
  memoryUsage Float // 内存使用率
  diskUsage   Float // 磁盘使用率
  networkIn   Float // 网络入流量（bytes）
  networkOut  Float // 网络出流量（bytes）
  activeUsers Int // 活跃用户数
  loadAverage Float? // 系统负载
  freeMemory  Int? // 空闲内存
  totalMemory Int? // 总内存
  createdAt   DateTime @default(now())

  @@index([createdAt])
}

// 错误日志
model ErrorLog {
  id        Int      @id @default(autoincrement())
  level     String // 错误级别
  message   String // 错误信息
  stack     String? // 错误堆栈
  userId    Int? // 关联的用户ID
  user      User?    @relation(fields: [userId], references: [id])
  metadata  String? // 额外的元数据，使用JSON字符串存储
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([level])
  @@index([createdAt])
}

// 系统告警
model Alert {
  id         Int       @id @default(autoincrement())
  type       String // system_error, security, performance
  level      String // critical, warning, info
  title      String
  message    String
  status     String // active, resolved
  solution   String? // 解决方案
  resolvedAt DateTime?
  resolvedBy Int? // 解决人ID
  resolver   User?     @relation("AlertResolver", fields: [resolvedBy], references: [id])
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@index([type])
  @@index([level])
  @@index([status])
  @@index([createdAt])
}

// 交易记录
model Transaction {
  id            Int      @id @default(autoincrement())
  type          String // 交易类型：充值、消费、退款、佣金等
  amount        Float // 交易金额
  status        String // 交易状态：pending、success、failed等
  userId        Int? // 关联的用户ID
  user          User?    @relation(fields: [userId], references: [id])
  agentId       Int? // 关联的代理商ID
  agent         Agent?   @relation(fields: [agentId], references: [id])
  paymentMethod String? // 支付方式：支付宝、微信等
  paymentId     String? // 支付平台交易号
  description   String? // 交易描述
  bankInfo      String? // 银行账户信息，使用JSON字符串存储
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联
  items               TransactionItem[]
  refundTransaction   Transaction?      @relation("RefundRelation", fields: [refundTransactionId], references: [id])
  refundTransactionId Int?
  refunds             Transaction[]     @relation("RefundRelation")

  @@index([userId])
  @@index([agentId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
}

// 交易商品明细
model TransactionItem {
  id        Int      @id @default(autoincrement())
  name      String // 商品名称
  quantity  Int // 数量
  price     Float // 单价
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联
  transactionId Int
  transaction   Transaction @relation(fields: [transactionId], references: [id])
}

// 工单模型
model WorkOrder {
  id         Int       @id @default(autoincrement())
  orderId    Int       @unique // 关联订单
  order      Order     @relation(fields: [orderId], references: [id])
  userId     Int // 客户ID
  user       User      @relation(fields: [userId], references: [id])
  documentId Int? // 关联文档
  document   Document? @relation(fields: [documentId], references: [id])

  // 工单基本信息
  title       String // 工单标题
  description String? // 工单描述
  serviceType String // 服务类型: 'ai_optimization', 'expert_optimization'
  priority    String  @default("normal") // 优先级: 'low', 'normal', 'high', 'urgent'
  status      String  @default("pending") // 状态: 'pending', 'assigned', 'in_progress', 'review', 'completed', 'cancelled'

  // 专家分配
  expertId   Int? // 分配的专家ID
  expert     Expert?   @relation(fields: [expertId], references: [id])
  assignedAt DateTime? // 分配时间

  // 时间管理
  estimatedHours Float? // 预估工时
  actualHours    Float? // 实际工时
  deadline       DateTime? // 截止时间

  // 文件管理
  originalContent  String? // 原始内容
  optimizedContent String? // 优化后内容
  attachments      WorkOrderAttachment[] // 附件

  // 沟通记录
  comments WorkOrderComment[] // 沟通记录
  workLogs ExpertWorkLog[] // 工作日志

  // 质量控制
  qualityScore   Float? // 质量评分
  clientFeedback String? // 客户反馈
  clientRating   Int? // 客户评分 1-5

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 专家模型
model Expert {
  id     Int  @id @default(autoincrement())
  userId Int  @unique // 关联用户账号
  user   User @relation(fields: [userId], references: [id])

  // 专家信息
  expertCode  String  @unique // 专家编号
  realName    String // 真实姓名
  title       String? // 职称
  education   String? // 学历
  specialties String // 专业领域 JSON数组
  languages   String // 语言能力 JSON数组

  // 工作状态
  status              String @default("active") // 状态: 'active', 'busy', 'offline', 'suspended'
  maxConcurrentOrders Int    @default(3) // 最大并发订单数
  currentOrders       Int    @default(0) // 当前订单数

  // 统计信息
  totalOrders     Int    @default(0) // 总订单数
  completedOrders Int    @default(0) // 完成订单数
  averageRating   Float? // 平均评分
  averageHours    Float? // 平均完成时间

  // 工单关联
  workOrders WorkOrder[]
  workLogs   ExpertWorkLog[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 工单附件
model WorkOrderAttachment {
  id          Int       @id @default(autoincrement())
  workOrderId Int
  workOrder   WorkOrder @relation(fields: [workOrderId], references: [id])
  fileName    String // 文件名
  filePath    String // 文件路径
  fileSize    Int // 文件大小
  fileType    String // 文件类型
  uploadedBy  Int // 上传者ID
  uploader    User      @relation(fields: [uploadedBy], references: [id])
  createdAt   DateTime  @default(now())
}

// 工单沟通记录
model WorkOrderComment {
  id          Int       @id @default(autoincrement())
  workOrderId Int
  workOrder   WorkOrder @relation(fields: [workOrderId], references: [id])
  authorId    Int // 评论者ID
  author      User      @relation(fields: [authorId], references: [id])
  content     String // 评论内容
  type        String    @default("comment") // 类型: 'comment', 'status_change', 'assignment'
  isInternal  Boolean   @default(false) // 是否内部评论
  createdAt   DateTime  @default(now())
}

// 专家工作日志
model ExpertWorkLog {
  id          Int       @id @default(autoincrement())
  expertId    Int
  expert      Expert    @relation(fields: [expertId], references: [id])
  workOrderId Int
  workOrder   WorkOrder @relation(fields: [workOrderId], references: [id])
  description String // 工作描述
  hoursSpent  Float // 花费时间
  logDate     DateTime  @default(now())
  createdAt   DateTime  @default(now())
}

// 服务配置
model ServiceConfig {
  id                    Int      @id @default(autoincrement())
  serviceType           String   @unique // 'ai_optimization', 'expert_optimization'
  name                  String // 服务名称
  description           String? // 服务描述
  pricePerThousandChars Float // 每千字符价格
  estimatedHours        Float? // 预估工时
  maxTurnaroundHours    Int? // 最大周转时间(小时)
  isActive              Boolean  @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}

// 临时访问记录表
model TempAccess {
  id         Int      @id @default(autoincrement())
  email      String   @unique
  accessCode String
  attempts   Int      @default(0)
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

// 临时文档表
model TempDocument {
  id           Int      @id @default(autoincrement())
  tempUserId   String // 临时用户ID
  email        String? // 可选的邮箱地址
  fileName     String
  originalName String?
  serviceType  String   @default("ai_optimization")
  status       String   @default("completed")
  downloadUrl  String?
  filePath     String?
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

// 临时订单表
model TempOrder {
  id               Int       @id @default(autoincrement())
  tempUserId       String // 临时用户ID
  tempUserEmail    String? // 临时用户邮箱（可选）
  amount           Float // 订单金额
  status           String // pending, paid, cancelled, processing, completed
  paymentMethod    String? // alipay, wechat, balance
  serviceType      String    @default("ai_optimization") // 服务类型
  description      String? // 订单描述
  processingStatus String? // processing, completed (专家处理状态)
  resultFilePath   String? // 处理结果文件路径
  resultFileName   String? // 处理结果文件名
  completedAt      DateTime? // 完成时间
  expiresAt        DateTime // 过期时间
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}

// 退款记录表
model Refund {
  id            Int       @id @default(autoincrement())
  orderId       Int? // 关联订单ID
  order         Order?    @relation(fields: [orderId], references: [id])
  tempOrderId   Int? // 关联临时订单ID（如果是临时用户）
  paymentId     Int? // 关联支付记录ID
  payment       Payment?  @relation(fields: [paymentId], references: [id])
  userId        Int? // 用户ID
  user          User?     @relation(fields: [userId], references: [id])
  tempUserId    String? // 临时用户ID
  amount        Float // 退款金额
  reason        String? // 退款原因
  status        String    @default("pending") // pending, approved, rejected, completed, failed
  processedBy   Int? // 处理人ID
  processedAt   DateTime? // 处理时间
  refundMethod  String? // 退款方式：alipay, wechat, balance
  refundTradeNo String? // 第三方退款交易号
  adminNote     String? // 管理员备注
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@index([orderId])
  @@index([paymentId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
}
