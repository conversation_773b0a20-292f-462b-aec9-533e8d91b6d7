
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PermissionScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  description: 'description',
  module: 'module',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  status: 'status',
  phone: 'phone',
  balance: 'balance',
  totalConsumption: 'totalConsumption',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLoginAt: 'lastLoginAt',
  lastLoginIp: 'lastLoginIp'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  content: 'content',
  optimizedContent: 'optimizedContent',
  fileName: 'fileName',
  originalName: 'originalName',
  serviceType: 'serviceType',
  status: 'status',
  filePath: 'filePath',
  downloadUrl: 'downloadUrl',
  expiresAt: 'expiresAt',
  orderId: 'orderId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  amount: 'amount',
  status: 'status',
  paymentMethod: 'paymentMethod',
  serviceType: 'serviceType',
  description: 'description',
  processingStatus: 'processingStatus',
  resultFilePath: 'resultFilePath',
  resultFileName: 'resultFileName',
  completedAt: 'completedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  orderId: 'orderId',
  realOrderId: 'realOrderId',
  amount: 'amount',
  type: 'type',
  status: 'status',
  outTradeNo: 'outTradeNo',
  tradeNo: 'tradeNo',
  paymentTime: 'paymentTime',
  verificationMethod: 'verificationMethod',
  payment_method: 'payment_method',
  fuiou_order_no: 'fuiou_order_no',
  fuiou_transaction_id: 'fuiou_transaction_id',
  paid_at: 'paid_at',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AgentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  agentName: 'agentName',
  level: 'level',
  commission: 'commission',
  totalSales: 'totalSales',
  totalCommission: 'totalCommission',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SettingScalarFieldEnum = {
  type: 'type',
  key: 'key',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AgreementScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  version: 'version',
  type: 'type',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AnnouncementScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  publishDate: 'publishDate',
  expireDate: 'expireDate',
  isPublished: 'isPublished',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ApiConfigScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  type: 'type',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CouponScalarFieldEnum = {
  id: 'id',
  code: 'code',
  type: 'type',
  value: 'value',
  minOrderAmount: 'minOrderAmount',
  usageLimit: 'usageLimit',
  usedCount: 'usedCount',
  expiresAt: 'expiresAt',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PromotionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  startDate: 'startDate',
  endDate: 'endDate',
  type: 'type',
  details: 'details',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.IpBlacklistScalarFieldEnum = {
  id: 'id',
  ipAddress: 'ipAddress',
  reason: 'reason',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SensitiveOperationLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  operation: 'operation',
  details: 'details',
  status: 'status',
  approverId: 'approverId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LoginLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  ip: 'ip',
  device: 'device',
  browser: 'browser',
  status: 'status',
  createdAt: 'createdAt'
};

exports.Prisma.OperationLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  target: 'target',
  details: 'details',
  ip: 'ip',
  createdAt: 'createdAt'
};

exports.Prisma.ApiLogScalarFieldEnum = {
  id: 'id',
  endpoint: 'endpoint',
  method: 'method',
  userId: 'userId',
  status: 'status',
  duration: 'duration',
  requestBody: 'requestBody',
  error: 'error',
  createdAt: 'createdAt'
};

exports.Prisma.SystemMetricScalarFieldEnum = {
  id: 'id',
  cpuUsage: 'cpuUsage',
  memoryUsage: 'memoryUsage',
  diskUsage: 'diskUsage',
  networkIn: 'networkIn',
  networkOut: 'networkOut',
  activeUsers: 'activeUsers',
  loadAverage: 'loadAverage',
  freeMemory: 'freeMemory',
  totalMemory: 'totalMemory',
  createdAt: 'createdAt'
};

exports.Prisma.ErrorLogScalarFieldEnum = {
  id: 'id',
  level: 'level',
  message: 'message',
  stack: 'stack',
  userId: 'userId',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AlertScalarFieldEnum = {
  id: 'id',
  type: 'type',
  level: 'level',
  title: 'title',
  message: 'message',
  status: 'status',
  solution: 'solution',
  resolvedAt: 'resolvedAt',
  resolvedBy: 'resolvedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  type: 'type',
  amount: 'amount',
  status: 'status',
  userId: 'userId',
  agentId: 'agentId',
  paymentMethod: 'paymentMethod',
  paymentId: 'paymentId',
  description: 'description',
  bankInfo: 'bankInfo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  refundTransactionId: 'refundTransactionId'
};

exports.Prisma.TransactionItemScalarFieldEnum = {
  id: 'id',
  name: 'name',
  quantity: 'quantity',
  price: 'price',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  transactionId: 'transactionId'
};

exports.Prisma.WorkOrderScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  userId: 'userId',
  documentId: 'documentId',
  title: 'title',
  description: 'description',
  serviceType: 'serviceType',
  priority: 'priority',
  status: 'status',
  expertId: 'expertId',
  assignedAt: 'assignedAt',
  estimatedHours: 'estimatedHours',
  actualHours: 'actualHours',
  deadline: 'deadline',
  originalContent: 'originalContent',
  optimizedContent: 'optimizedContent',
  qualityScore: 'qualityScore',
  clientFeedback: 'clientFeedback',
  clientRating: 'clientRating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ExpertScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  expertCode: 'expertCode',
  realName: 'realName',
  title: 'title',
  education: 'education',
  specialties: 'specialties',
  languages: 'languages',
  status: 'status',
  maxConcurrentOrders: 'maxConcurrentOrders',
  currentOrders: 'currentOrders',
  totalOrders: 'totalOrders',
  completedOrders: 'completedOrders',
  averageRating: 'averageRating',
  averageHours: 'averageHours',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkOrderAttachmentScalarFieldEnum = {
  id: 'id',
  workOrderId: 'workOrderId',
  fileName: 'fileName',
  filePath: 'filePath',
  fileSize: 'fileSize',
  fileType: 'fileType',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt'
};

exports.Prisma.WorkOrderCommentScalarFieldEnum = {
  id: 'id',
  workOrderId: 'workOrderId',
  authorId: 'authorId',
  content: 'content',
  type: 'type',
  isInternal: 'isInternal',
  createdAt: 'createdAt'
};

exports.Prisma.ExpertWorkLogScalarFieldEnum = {
  id: 'id',
  expertId: 'expertId',
  workOrderId: 'workOrderId',
  description: 'description',
  hoursSpent: 'hoursSpent',
  logDate: 'logDate',
  createdAt: 'createdAt'
};

exports.Prisma.ServiceConfigScalarFieldEnum = {
  id: 'id',
  serviceType: 'serviceType',
  name: 'name',
  description: 'description',
  pricePerThousandChars: 'pricePerThousandChars',
  estimatedHours: 'estimatedHours',
  maxTurnaroundHours: 'maxTurnaroundHours',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TempAccessScalarFieldEnum = {
  id: 'id',
  email: 'email',
  accessCode: 'accessCode',
  attempts: 'attempts',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TempDocumentScalarFieldEnum = {
  id: 'id',
  tempUserId: 'tempUserId',
  email: 'email',
  fileName: 'fileName',
  originalName: 'originalName',
  serviceType: 'serviceType',
  status: 'status',
  downloadUrl: 'downloadUrl',
  filePath: 'filePath',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TempOrderScalarFieldEnum = {
  id: 'id',
  tempUserId: 'tempUserId',
  tempUserEmail: 'tempUserEmail',
  amount: 'amount',
  status: 'status',
  paymentMethod: 'paymentMethod',
  serviceType: 'serviceType',
  description: 'description',
  processingStatus: 'processingStatus',
  resultFilePath: 'resultFilePath',
  resultFileName: 'resultFileName',
  completedAt: 'completedAt',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RefundScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  tempOrderId: 'tempOrderId',
  paymentId: 'paymentId',
  userId: 'userId',
  tempUserId: 'tempUserId',
  amount: 'amount',
  reason: 'reason',
  status: 'status',
  processedBy: 'processedBy',
  processedAt: 'processedAt',
  refundMethod: 'refundMethod',
  refundTradeNo: 'refundTradeNo',
  adminNote: 'adminNote',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  Role: 'Role',
  Permission: 'Permission',
  User: 'User',
  Document: 'Document',
  Order: 'Order',
  Payment: 'Payment',
  Agent: 'Agent',
  Setting: 'Setting',
  Agreement: 'Agreement',
  Announcement: 'Announcement',
  ApiConfig: 'ApiConfig',
  Coupon: 'Coupon',
  Promotion: 'Promotion',
  IpBlacklist: 'IpBlacklist',
  SensitiveOperationLog: 'SensitiveOperationLog',
  LoginLog: 'LoginLog',
  OperationLog: 'OperationLog',
  ApiLog: 'ApiLog',
  SystemMetric: 'SystemMetric',
  ErrorLog: 'ErrorLog',
  Alert: 'Alert',
  Transaction: 'Transaction',
  TransactionItem: 'TransactionItem',
  WorkOrder: 'WorkOrder',
  Expert: 'Expert',
  WorkOrderAttachment: 'WorkOrderAttachment',
  WorkOrderComment: 'WorkOrderComment',
  ExpertWorkLog: 'ExpertWorkLog',
  ServiceConfig: 'ServiceConfig',
  TempAccess: 'TempAccess',
  TempDocument: 'TempDocument',
  TempOrder: 'TempOrder',
  Refund: 'Refund'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
