const paymentService = require('../services/paymentService');
const FuiouService = require('../services/fuiouService');
const { PrismaClient } = require('@prisma/client');
const axios = require('axios');
const fuiouConfig = require('../config/fuiouConfig');

const prisma = new PrismaClient();

/**
 * 检测文本是否为乱码
 * @param {string} text - 要检测的文本
 * @returns {boolean} - 是否为乱码
 */
function isGarbledText(text) {
    if (!text) return false;

    // 检测常见的UTF-8乱码特征
    const garbledPatterns = [
        /[æçèéêëìíîï]/g,  // 常见的UTF-8乱码字符
        /[ÃÂÁÀÄÅÇÈÉÊËÌÍÎÏÑÒÓÔÕÖØÙÚÛÜÝß]/g, // 其他编码问题字符
    ];

    return garbledPatterns.some(pattern => pattern.test(text));
}

/**
 * 支付控制器
 */
class PaymentController {
  /**
   * 创建订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createOrder(req, res) {
    try {
      const { amount, description, serviceType, content, email } = req.body;
      const userId = req.user.userId || req.user.id;
      const isTempUser = req.isTempUser || false;

      console.log(`💰 创建订单 - 用户类型: ${isTempUser ? '临时用户' : '正式用户'}, ID: ${userId}`);

      if (!amount || amount <= 0) {
        return res.status(400).json({ error: '无效的订单金额' });
      }

      // 映射前端serviceType到数据库字段
      const serviceTypeMapping = {
        'ai': 'ai_optimization',
        'expert': 'expert_optimization'
      };

      const dbServiceType = serviceTypeMapping[serviceType] || 'ai_optimization';

      // 根据服务类型设置不同的描述和状态
      let orderDescription = description || '文本优化服务';
      let orderStatus = 'pending';
      let orderData = {
        userId: isTempUser ? userId : parseInt(userId) || 1, // 临时用户保持字符串，正式用户转为数字
        amount,
        description: orderDescription,
        serviceType: dbServiceType,
        status: orderStatus
      };

      // 如果是临时用户且提供了邮箱，保存邮箱信息
      if (isTempUser && email) {
        orderData.tempUserEmail = email;
        console.log(`📧 临时用户提供邮箱: ${email}`);
      }

      // 如果是学术专家服务，保持前端传来的完整描述（包含文档名称）
      if (serviceType === 'expert') {
        // 不再强制覆盖描述，使用前端传来的完整描述
        console.log(`📄 学术专家服务订单描述: ${orderDescription}`);
        // 注意：订单状态应该保持为 'pending'，只有支付成功后才变为 'paid'
      }

      const order = await paymentService.createOrder(
        isTempUser ? userId : parseInt(userId) || 1,
        amount,
        orderDescription,
        orderData
      );

      return res.status(201).json({ order });
    } catch (error) {
      console.error('创建订单失败:', error);
      return res.status(500).json({ error: '创建订单失败', message: error.message });
    }
  }
  
  /**
   * 创建支付宝支付
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async createAlipayPayment(req, res) {
    try {
        const { orderId, subject = '文本优化服务' } = req.body;
        console.log('创建支付宝支付请求:', { orderId, subject });
        if (!orderId) {
            console.error('创建支付宝支付失败: 缺少订单ID');
            return res.status(400).json({ success: false, message: '订单ID不能为空' });
        }

        // 获取用户ID
        const userId = req.user.userId || req.user.id || 1; // 兼容不同的用户ID字段
        console.log('用户ID:', userId);

        // 获取User-Agent用于设备检测
        const userAgent = req.headers['user-agent'] || '';
        console.log('User-Agent:', userAgent);

        // 创建支付宝支付
        const result = await paymentService.createAlipayPayment(userId, orderId, subject, userAgent);
        console.log('支付宝支付创建成功，返回表单HTML');
        
        // 返回支付表单
        return res.status(200).json({
            success: true,
            message: '支付宝支付创建成功',
            paymentId: result.paymentId,
            paymentUrl: result.formHtml
        });
    } catch (error) {
        console.error('创建支付宝支付失败:', error);
        return res.status(500).json({ success: false, message: '创建支付宝支付失败: ' + error.message });
    }
  }
  
  /**
   * 处理支付宝支付通知
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async alipayNotify(req, res) {
    try {
        console.log('收到支付宝回调通知:', req.body);
        
        // 验证支付宝通知
        const isValid = await paymentService.verifyAlipayNotify(req.body);
        if (!isValid) {
            console.error('支付宝通知验证失败');
            return res.status(400).send('fail');
        }

        // 处理支付宝通知
        const result = await paymentService.processAlipayNotify(req.body);
        if (result.success) {
            console.log('支付宝通知处理成功:', result);
            return res.send('success');
        } else {
            console.error('支付宝通知处理失败:', result.message);
            return res.status(400).send('fail');
        }
    } catch (error) {
        console.error('处理支付宝通知失败:', error);
        return res.status(500).send('fail');
    }
  }
  
  /**
   * 处理支付宝支付返回
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async alipayReturn(req, res) {
    try {
        console.log('收到支付宝同步返回:', req.query);
        
        // 从查询参数中获取订单号和金额
        const outTradeNo = req.query.out_trade_no;
        const totalAmount = req.query.total_amount;
        const tradeNo = req.query.trade_no;

        // 构建重定向URL，将所有相关参数传递给前端
        const redirectUrl = new URL(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/payment-result.html`);
        redirectUrl.searchParams.set('out_trade_no', outTradeNo);
        redirectUrl.searchParams.set('total_amount', totalAmount);
        redirectUrl.searchParams.set('trade_no', tradeNo);
        redirectUrl.searchParams.set('status', 'processing'); // 明确告知前端正在处理中
        redirectUrl.searchParams.set('method', 'alipay.trade.page.pay.return');

        console.log('重定向到前端支付结果页面:', redirectUrl.toString());
        
        // 直接重定向到前端，不处理任何业务逻辑
        return res.redirect(redirectUrl.toString());
    } catch (error) {
        console.error('处理支付宝同步返回失败:', error);
        // 即使出错，也尝试重定向到前端的错误页面
        const errorRedirectUrl = new URL(`${process.env.FRONTEND_URL || 'http://localhost:3000'}/payment-result.html`);
        errorRedirectUrl.searchParams.set('status', 'error');
        errorRedirectUrl.searchParams.set('message', '处理支付返回时发生内部错误');
        return res.redirect(errorRedirectUrl.toString());
    }
  }
  
  /**
   * 查询支付状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async queryPaymentStatus(req, res) {
    try {
      const { identifier } = req.params; // 可以是 paymentId 或 outTradeNo
      const userId = req.user.userId || req.user.id || 1; // 兼容不同的用户ID字段
      
      // 查询支付记录
      const payment = await prisma.payment.findFirst({
        where: {
          OR: [
            { id: identifier },
            { outTradeNo: identifier }
          ],
          userId,
        },
        // 移除错误的include，Payment模型中没有order关联
      });
      
      if (!payment) {
        return res.status(404).json({ error: '支付记录不存在或无权访问' });
      }
      
      // 如果支付状态是pending，则查询支付宝交易状态
      if (payment.status === 'pending') {
        const tradeResult = await paymentService.queryAlipayTrade(payment.outTradeNo);
        
        // 根据查询结果更新支付状态
        if (tradeResult.code === '10000') {
          const tradeStatus = tradeResult.trade_status;
          
          if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'TRADE_FINISHED') {
            // 更新支付记录
            await prisma.payment.update({
              where: { id: payment.id },
              data: {
                status: 'success',
                tradeNo: tradeResult.trade_no,
                paymentTime: new Date(tradeResult.send_pay_date),
              },
            });
            
            // 更新订单状态
            await prisma.order.update({
              where: { id: payment.orderId },
              data: { status: 'paid' },
            });
            
            // 重新查询支付记录
            payment.status = 'success';
            payment.tradeNo = tradeResult.trade_no;
            payment.paymentTime = new Date(tradeResult.send_pay_date);
            payment.order.status = 'paid';
          } else if (tradeStatus === 'TRADE_CLOSED') {
            // 更新支付记录
            await prisma.payment.update({
              where: { id: payment.id },
              data: {
                status: 'failed',
                tradeNo: tradeResult.trade_no,
              },
            });
            
            // 重新查询支付记录
            payment.status = 'failed';
            payment.tradeNo = tradeResult.trade_no;
          }
        }
      }
      
      return res.status(200).json({ payment });
    } catch (error) {
      console.error('查询支付状态失败:', error);
      return res.status(500).json({ error: '查询支付状态失败' });
    }
  }
  
  /**
   * 获取用户订单列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getUserOrders(req, res) {
    try {
      const userId = req.user.userId || req.user.id || 1; // 兼容不同的用户ID字段
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      
      const result = await paymentService.getUserOrders(userId, page, pageSize);
      
      return res.status(200).json(result);
    } catch (error) {
      console.error('获取用户订单列表失败:', error);
      return res.status(500).json({ error: '获取用户订单列表失败' });
    }
  }
  
  /**
   * 获取订单详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrderDetails(req, res) {
    try {
      const { orderId } = req.params;
      const userId = req.user.userId || req.user.id || 1; // 兼容不同的用户ID字段
      
      const order = await paymentService.getOrderDetails(orderId, userId);
      
      return res.status(200).json({ order });
    } catch (error) {
      console.error('获取订单详情失败:', error);
      return res.status(500).json({ error: '获取订单详情失败' });
    }
  }

  /**
   * 验证支付状态（安全验证，防止客户端伪造）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async verifyPayment(req, res) {
    try {
      const { paymentId } = req.params;
      const userId = req.user.userId || req.user.id;
      const isTempUser = req.isTempUser || false;

      console.log('验证支付状态:', { paymentId, userId, isTempUser });

      let payment = null;
      let order = null;

      if (isTempUser) {
        // 临时用户：通过paymentId查找对应的TempOrder
        console.log('📱 临时用户支付验证，查找TempOrder');

        // 从paymentId中提取订单ID（假设paymentId格式为 PAY_timestamp_xxx）
        // 或者直接通过支付ID查找，这里我们需要一个映射机制
        // 暂时使用简化方案：查找最近的临时订单
        const tempOrders = await prisma.tempOrder.findMany({
          where: {
            tempUserId: userId,
            status: 'pending'
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 1
        });

        if (tempOrders.length > 0) {
          order = tempOrders[0];
          // 创建虚拟支付对象
          payment = {
            id: paymentId,
            orderId: order.id,
            userId: userId,
            status: 'success', // 假设支付成功（实际应该查询支付宝状态）
            outTradeNo: paymentId,
            order: order
          };
          console.log('📱 找到临时订单:', order.id);
        }
      } else {
        // 正式用户：查询Payment表
        console.log('👤 正式用户支付验证，查找Payment记录');
        payment = await prisma.payment.findFirst({
          where: {
            id: parseInt(paymentId),
            userId: parseInt(userId)
          }
          // 移除错误的include，Payment模型中没有order关联
        });
      }

      if (!payment) {
        return res.status(404).json({
          success: false,
          verified: false,
          message: '支付记录不存在或无权访问'
        });
      }

      console.log('支付记录详情:', {
        paymentId: payment.id,
        orderId: payment.orderId,
        orderType: payment.order?.serviceType,
        status: payment.status
      });

      // 临时用户简化验证：直接返回成功（因为用户已经完成支付跳转）
      if (isTempUser) {
        console.log('📱 临时用户支付验证，直接返回成功状态');
        // 更新临时订单状态为已支付
        await prisma.tempOrder.update({
          where: { id: order.id },
          data: {
            status: 'paid',
            completedAt: new Date(),
            paymentMethod: 'alipay' // 保存支付方式
          }
        });

        return res.json({
          success: true,
          verified: true,
          status: 'paid',
          message: '支付验证成功',
          orderType: order.serviceType
        });
      }

      // 正式用户：如果支付状态是pending，主动查询支付宝状态（本地环境异步通知无法工作）
      if (payment.status === 'pending' && payment.outTradeNo) {
        try {
          console.log(`[支付验证] 支付状态为pending，主动查询支付宝状态，订单号: ${payment.outTradeNo}`);

          // 主动查询支付宝交易状态
          const tradeResult = await paymentService.queryAlipayTrade(payment.outTradeNo);
          console.log(`[支付验证] 支付宝查询结果:`, {
            code: tradeResult.code,
            msg: tradeResult.msg,
            tradeStatus: tradeResult.tradeStatus
          });

          if (tradeResult.code === '10000') {
            const tradeStatus = tradeResult.tradeStatus;
            console.log(`[支付验证] 交易状态: ${tradeStatus}`);

            if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'TRADE_FINISHED') {
              if (isTempUser) {
                // 临时用户：更新TempOrder状态
                console.log('📱 临时用户支付成功，更新TempOrder状态');
                await prisma.tempOrder.update({
                  where: { id: order.id },
                  data: {
                    status: 'paid',
                    completedAt: new Date(),
                    paymentMethod: 'alipay',
                    alipayTradeNo: tradeResult.tradeNo // 保存支付宝交易号
                  }
                });
                payment.status = 'success';
              } else {
                // 正式用户：更新Payment记录
                console.log('👤 正式用户支付成功，更新Payment记录');
                await prisma.payment.update({
                  where: { id: payment.id },
                  data: {
                    status: 'success',
                    tradeNo: tradeResult.tradeNo,
                    paymentTime: new Date(tradeResult.sendPayDate || new Date()),
                    verificationMethod: 'query', // 标记验证方式
                  },
                });
                payment.status = 'success';
              }

              console.log(`[支付验证] 支付状态已更新为成功（主动查询验证）`);
            } else {
              console.log(`[支付验证] 交易状态不是成功状态: ${tradeStatus}`);
            }
          } else {
            console.log(`[支付验证] 支付宝查询失败，错误码: ${tradeResult.code}, 错误信息: ${tradeResult.msg}`);
          }
        } catch (queryError) {
          console.error('[支付验证] 查询支付宝交易状态失败:', queryError);
        }
      } else {
        console.log(`[支付验证] 支付状态: ${payment.status}`);
      }

      // 返回验证结果
      const isVerified = payment.status === 'success';

      // 确定订单类型
      let orderType = 'ai'; // 默认为AI优化
      if (payment.order && payment.order.serviceType) {
        orderType = payment.order.serviceType === 'expert_optimization' ? 'expert' : 'ai';
      }

      console.log('支付验证结果:', {
        verified: isVerified,
        orderType: orderType,
        serviceType: payment.order?.serviceType
      });

      return res.status(200).json({
        success: true,
        verified: isVerified,
        status: isVerified ? 'paid' : payment.status,
        message: isVerified ? '支付验证成功' : '支付未完成',
        paymentId: payment.id,
        orderId: payment.orderId,
        amount: payment.amount,
        orderType: orderType,  // 添加订单类型
        serviceType: payment.order?.serviceType  // 添加详细服务类型
      });

    } catch (error) {
      console.error('验证支付状态失败:', error);
      return res.status(500).json({
        success: false,
        verified: false,
        message: '验证支付状态失败: ' + error.message
      });
    }
  }

  /**
   * 取消订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async cancelOrder(req, res) {
    try {
      const { orderId } = req.params;
      const userId = req.user.userId || req.user.id || 1; // 兼容不同的用户ID字段
      
      // 获取订单详情
      const order = await paymentService.getOrderDetails(orderId, userId);
      
      if (order.status !== 'pending') {
        return res.status(400).json({ error: '只能取消待支付订单' });
      }
      
      // 查找关联的支付记录
      const payment = order.payments.find(p => p.status === 'pending');
      
      if (payment) {
        // 关闭支付宝交易
        await paymentService.closeAlipayTrade(payment.outTradeNo);
      }
      
      // 更新订单状态
      await prisma.order.update({
        where: { id: orderId },
        data: { status: 'cancelled' },
      });
      
      return res.status(200).json({ message: '订单已取消' });
    } catch (error) {
      console.error('取消订单失败:', error);
      return res.status(500).json({ error: '取消订单失败' });
    }
  }

  /**
   * 查询支付状态（用于支付成功页面检查异步通知）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async queryPaymentStatus(req, res) {
    try {
      const { identifier } = req.params; // 可以是outTradeNo或paymentId

      console.log(`[状态查询] 查询支付状态，标识符: ${identifier}`);

      // 先尝试按outTradeNo查询
      let payment = await prisma.payment.findFirst({
        where: { outTradeNo: identifier }
      });

      // 如果没找到，尝试按ID查询
      if (!payment && !isNaN(identifier)) {
        payment = await prisma.payment.findFirst({
          where: { id: parseInt(identifier) }
        });
      }

      if (!payment) {
        return res.status(404).json({
          success: false,
          message: '支付记录不存在'
        });
      }

      console.log(`[状态查询] 支付状态: ${payment.status}, 验证方式: ${payment.verificationMethod}`);

      return res.json({
        success: true,
        status: payment.status,
        verificationMethod: payment.verificationMethod,
        paymentTime: payment.paymentTime,
        amount: payment.amount
      });

    } catch (error) {
      console.error('[状态查询] 查询支付状态失败:', error);
      return res.status(500).json({
        success: false,
        message: '查询支付状态失败'
      });
    }
  }

  /**
   * 根据商户订单号检查支付状态（用于支付成功页面）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async checkPaymentByTradeNo(req, res) {
    try {
      const { outTradeNo } = req.params;

      if (!outTradeNo) {
        return res.status(400).json({
          success: false,
          message: '缺少订单号'
        });
      }

      console.log(`[支付检查] 检查订单号 ${outTradeNo} 的支付状态`);

      // 查询支付记录
      let payment = await prisma.payment.findFirst({
        where: { outTradeNo: outTradeNo }
      });

      if (!payment) {
        return res.status(404).json({
          success: false,
          message: '支付记录不存在'
        });
      }

      console.log(`[支付检查] 订单 ${outTradeNo} 当前状态: ${payment.status}`);

      // 如果状态是pending，主动查询支付宝状态
      if (payment.status === 'pending') {
        console.log(`[支付检查] 状态为pending，主动查询支付宝状态`);

        try {
          const tradeResult = await paymentService.queryAlipayTrade(outTradeNo);

          if (tradeResult.code === '10000') {
            const tradeStatus = tradeResult.tradeStatus;
            console.log(`[支付检查] 支付宝查询结果: ${tradeStatus}`);

            if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'TRADE_FINISHED') {
              // 更新支付状态
              payment = await prisma.payment.update({
                where: { id: payment.id },
                data: {
                  status: 'success',
                  tradeNo: tradeResult.tradeNo,
                  paymentTime: new Date(tradeResult.sendPayDate || new Date()),
                  verificationMethod: 'query_from_page', // 标记为页面查询验证
                },
              });

              console.log(`[支付检查] 支付状态已更新为成功（页面查询验证）`);
            }
          }
        } catch (queryError) {
          console.error(`[支付检查] 查询支付宝状态失败:`, queryError);
          // 查询失败不影响返回当前状态
        }
      }

      return res.json({
        success: true,
        status: payment.status,
        verificationMethod: payment.verificationMethod,
        paymentTime: payment.paymentTime,
        amount: payment.amount
      });

    } catch (error) {
      console.error('[支付检查] 检查支付状态失败:', error);
      return res.status(500).json({
        success: false,
        message: '检查支付状态失败'
      });
    }
  }

  /**
   * 更新订单处理结果（专家上传处理结果）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateOrderProcessingResult(req, res) {
    try {
      const { orderId } = req.params;

      // 检查是否有上传的文件
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请上传处理结果文件'
        });
      }

      const files = req.files;
      const resultFilePaths = files.map(file => file.path);
      const resultFileNames = files.map(file => file.originalname);

      console.log(`处理订单 ${orderId} 的结果文件上传:`, {
        fileCount: files.length,
        files: files.map(file => ({
          originalName: file.originalname,
          filePath: file.path,
          size: file.size
        }))
      });

      const updatedOrder = await paymentService.updateOrderProcessingResult(
        parseInt(orderId),
        resultFilePaths,
        resultFileNames
      );

      return res.status(200).json({
        success: true,
        message: `${files.length}个处理结果文件上传成功`,
        order: updatedOrder
      });
    } catch (error) {
      console.error('更新订单处理结果失败:', error);
      return res.status(500).json({
        success: false,
        message: error.message || '更新订单处理结果失败'
      });
    }
  }

  /**
   * 获取处理中的订单列表（管理后台使用）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getProcessingOrders(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 20;

      const result = await paymentService.getProcessingOrders(page, pageSize);

      return res.status(200).json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error('获取处理中订单列表失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取处理中订单列表失败'
      });
    }
  }

  /**
   * 下载订单处理结果文件
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async downloadOrderResult(req, res) {
    try {
      const { orderId, fileIndex } = req.params;
      const userId = req.user.userId || req.user.id;

      // 获取订单信息
      const order = await paymentService.getOrderDetails(parseInt(orderId), userId);

      if (!order) {
        return res.status(404).json({
          success: false,
          message: '订单不存在'
        });
      }

      if (order.serviceType !== 'expert_optimization') {
        return res.status(400).json({
          success: false,
          message: '该订单不支持文件下载'
        });
      }

      if (order.processingStatus !== 'completed' || !order.resultFilePath) {
        return res.status(400).json({
          success: false,
          message: '订单处理结果尚未完成或文件不存在'
        });
      }

      const fs = require('fs');
      const path = require('path');

      // 解析文件路径和文件名（支持多文件）
      let filePaths, fileNames;

      try {
        // 尝试解析为JSON数组（新格式）
        filePaths = JSON.parse(order.resultFilePath);
        fileNames = JSON.parse(order.resultFileName);
      } catch (e) {
        // 如果解析失败，说明是旧格式（单文件）
        filePaths = [order.resultFilePath];
        fileNames = [order.resultFileName];
      }

      // 确定要下载的文件索引
      const index = fileIndex ? parseInt(fileIndex) : 0;

      if (index < 0 || index >= filePaths.length) {
        return res.status(400).json({
          success: false,
          message: '文件索引无效'
        });
      }

      const targetFilePath = filePaths[index];
      const targetFileName = fileNames[index];

      // 检查文件是否存在
      if (!fs.existsSync(targetFilePath)) {
        return res.status(404).json({
          success: false,
          message: '处理结果文件不存在'
        });
      }

      // 设置下载响应头
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(targetFileName)}"`);
      res.setHeader('Content-Type', 'application/octet-stream');

      // 发送文件
      res.sendFile(path.resolve(targetFilePath));

    } catch (error) {
      console.error('下载订单结果文件失败:', error);
      return res.status(500).json({
        success: false,
        message: '下载文件失败'
      });
    }
  }

  /**
   * 获取统一的文档历史（AI优化 + 专家服务）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getDocumentHistory(req, res) {
    try {
      const userId = req.user.userId || req.user.id;
      const isTempUser = req.isTempUser || false;
      const { page = 1, pageSize = 50 } = req.query;

      console.log(`获取用户 ${userId} 的文档历史，页码: ${page}, 页大小: ${pageSize}, 临时用户: ${isTempUser}`);

      let aiDocuments = [];
      let expertOrders = [];

      if (isTempUser) {
        // 临时用户：查询TempDocument和TempOrder
        console.log('📱 获取临时用户文档历史');

        // 获取临时文档（这里我们假设临时用户的优化内容存储在某个地方，或者通过其他方式获取）
        const tempDocuments = await prisma.tempDocument.findMany({
          where: {
            tempUserId: userId
          },
          orderBy: {
            createdAt: 'desc'
          }
        });

        // 转换为统一格式
        aiDocuments = tempDocuments.map(doc => {
          // 智能文件名处理：确保显示正确的文件名
          let displayFileName;
          if (doc.fileName && doc.fileName !== '未命名文档+优化版.docx') {
            displayFileName = doc.fileName;
          } else if (doc.originalName && doc.originalName !== '未命名文档') {
            const nameWithoutExt = doc.originalName.replace(/\.[^/.]+$/, '');
            displayFileName = `${nameWithoutExt}+优化版.docx`;
          } else {
            displayFileName = '文档+优化版.docx';
          }

          console.log(`临时文档${doc.id}文件名处理: fileName=${doc.fileName}, originalName=${doc.originalName}, 最终=${displayFileName}`);

          return {
            id: doc.id,
            title: displayFileName,
            fileName: displayFileName,
            originalName: doc.originalName,
            serviceType: doc.serviceType,
            status: doc.status,
            downloadUrl: doc.downloadUrl,
            expiresAt: doc.expiresAt,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
            optimizedContent: null, // 临时文档的优化内容需要从其他地方获取
            isTemp: true
          };
        });

        // 获取临时订单（专家服务）
        const tempExpertOrders = await prisma.tempOrder.findMany({
          where: {
            tempUserId: userId,
            serviceType: 'expert_optimization',
            status: 'paid'
          }
        });

        expertOrders = tempExpertOrders.map(order => ({
          ...order,
          user: { email: order.tempUserEmail },
          isTemp: true
        }));

      } else {
        // 正式用户：查询Document和Order表
        console.log('👤 获取正式用户文档历史');

        // 获取AI优化文档（包括新的AI优化记录）
        aiDocuments = await prisma.document.findMany({
          where: {
            userId: parseInt(userId),
            OR: [
              {
                optimizedContent: {
                  not: null
                }
              },
              {
                serviceType: 'ai_optimization'
              }
            ]
          },
          select: {
            id: true,
            title: true,
            fileName: true,
            originalName: true,
            serviceType: true,
            status: true,
            downloadUrl: true,
            expiresAt: true,
            createdAt: true,
            updatedAt: true,
            optimizedContent: true
          }
        });

        // 获取专家服务订单（已支付且非取消状态）
        expertOrders = await prisma.order.findMany({
          where: {
            userId: parseInt(userId),
            serviceType: 'expert_optimization',
            status: 'paid' // 只显示已支付的订单
          },
          include: {
            user: true
          }
        });
      }

      // 统一格式化文档数据
      const allDocuments = [];

      console.log(`找到 ${aiDocuments.length} 个AI优化文档，${expertOrders.length} 个专家服务订单`);

      // 处理AI优化文档
      aiDocuments.forEach(doc => {
        // 只有有优化内容的文档才显示
        if (doc.optimizedContent) {
          // 智能文件名处理：确保显示正确的文件名
          let fileName;
          if (doc.fileName && doc.fileName !== '粘贴文本.docx') {
            // 如果有fileName且不是默认的"粘贴文本.docx"，使用它
            fileName = doc.fileName;
          } else if (doc.originalName && doc.originalName !== '粘贴文本') {
            // 如果有originalName且不是默认的"粘贴文本"，基于它生成
            const nameWithoutExt = doc.originalName.replace(/\.[^/.]+$/, '');
            fileName = `${nameWithoutExt}+优化版.docx`;
          } else if (doc.title && doc.title !== '粘贴文本') {
            // 如果title不是默认的"粘贴文本"，基于它生成
            const nameWithoutExt = doc.title.replace(/\.[^/.]+$/, '');
            fileName = `${nameWithoutExt}+优化版.docx`;
          } else {
            // 最后的备用方案
            fileName = '文档+优化版.docx';
          }

          console.log(`文档${doc.id}文件名处理: title=${doc.title}, fileName=${doc.fileName}, originalName=${doc.originalName}, 最终=${fileName}`);

          allDocuments.push({
            id: `ai_${doc.id}`,
            fileName: fileName,
            originalName: doc.originalName || doc.title,
            serviceType: 'ai_optimization',
            status: 'completed',
            downloadUrl: `/api/documents/download/${doc.id}`,
            createdAt: doc.createdAt,
            completedAt: doc.updatedAt,
            expiresAt: doc.expiresAt || new Date(new Date(doc.updatedAt).getTime() + 30 * 24 * 60 * 60 * 1000) // 30天后过期
          });
        }
      });

      // 处理专家服务订单
      expertOrders.forEach(order => {
        // 修复状态判断：只有processingStatus为'completed'才是已完成
        const isCompleted = order.processingStatus === 'completed';
        const status = isCompleted ? 'completed' : 'processing';

        // 处理多文件情况
        let fileNames = [];
        let filePaths = [];

        try {
          // 尝试解析为JSON数组（新格式）
          if (order.resultFileName && order.resultFileName.startsWith('[')) {
            fileNames = JSON.parse(order.resultFileName);
            filePaths = JSON.parse(order.resultFilePath || '[]');
          } else {
            // 旧格式（单文件）
            fileNames = order.resultFileName ? [order.resultFileName] : [];
            filePaths = order.resultFilePath ? [order.resultFilePath] : [];
          }
        } catch (e) {
          // 解析失败，使用默认值
          fileNames = order.resultFileName ? [order.resultFileName] : [];
          filePaths = order.resultFilePath ? [order.resultFilePath] : [];
        }

        // 如果没有文件名，从订单描述生成（默认使用.docx）
        if (fileNames.length === 0 && order.description) {
          const documentName = order.description.replace(/^学术专家润色服务\s*-\s*/, '');
          fileNames = [`${documentName}+优化版.docx`];
        }

        // 为每个文件创建文档记录
        fileNames.forEach((fileName, index) => {
          // 智能文件名处理：避免乱码问题
          let cleanFileName = fileName;
          if (isGarbledText(fileName) && order.description) {
            const documentName = order.description.replace(/^学术专家润色服务\s*-\s*/, '');
            // 保持原始文件扩展名，如果无法确定则使用.docx
            const originalExt = fileName.match(/\.[^/.]+$/)?.[0] || '.docx';
            cleanFileName = `${documentName}+优化版${originalExt}`;
          }

          console.log(`订单${order.id}文件${index}名处理: 原始=${fileName}, 最终=${cleanFileName}`);

          allDocuments.push({
            id: `expert_${order.id}_${index}`,
            fileName: cleanFileName,
            originalName: cleanFileName.replace('+优化版', '').replace(/\.[^/.]+$/, ''),
            serviceType: 'expert_optimization',
            status: status,
            downloadUrl: isCompleted ? `/api/payments/orders/${order.id}/download/${index}` : null,
            createdAt: order.createdAt,
            completedAt: order.completedAt,
            expiresAt: isCompleted && order.completedAt ?
              new Date(new Date(order.completedAt).getTime() + 30 * 24 * 60 * 60 * 1000) : null,
            amount: order.amount,
            orderId: order.id,
            fileIndex: index // 添加文件索引
          });
        });
      });

      // 排序：处理中的在前，然后按时间倒序
      allDocuments.sort((a, b) => {
        // 处理中的优先
        if (a.status === 'processing' && b.status !== 'processing') {
          return -1;
        }
        if (b.status === 'processing' && a.status !== 'processing') {
          return 1;
        }

        // 同状态按时间倒序
        return new Date(b.createdAt) - new Date(a.createdAt);
      });

      // 分页
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + parseInt(pageSize);
      const paginatedDocuments = allDocuments.slice(startIndex, endIndex);

      return res.status(200).json({
        success: true,
        documents: paginatedDocuments,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: allDocuments.length,
          totalPages: Math.ceil(allDocuments.length / pageSize)
        }
      });

    } catch (error) {
      console.error('获取文档历史失败:', error);
      return res.status(500).json({
        success: false,
        message: '获取文档历史失败'
      });
    }
  }

  /**
   * 创建富有支付订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createFuiouPayment(req, res) {
    try {
      const { amount, description, orderId } = req.body;
      const userId = req.user.id;

      console.log(`💰 创建富有支付 - 用户ID: ${userId}, 金额: ${amount}, 描述: ${description}`);

      // 🎯 第一步：按照富有支付文档调用统一下单接口
      // 生成符合富有支付规则的订单号
      const generateFuiouOrderId = () => {
        const prefix = process.env.FUIOU_ORDER_PREFIX || '18927'; // 5位商户号编码
        const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
        const randomNum = Math.random().toString().slice(2, 10); // 8位随机数
        return `${prefix}${date}${randomNum}`;
      };

      const finalOrderId = orderId || generateFuiouOrderId();

      // 🚀 调用富有支付服务（使用现有的服务）
      console.log('🔄 调用富有支付服务...');

      const paymentResult = await FuiouService.createPayment({
        orderId: finalOrderId,
        amount: parseFloat(amount),
        description: description || '文档优化服务'
      });

      console.log('📥 富有支付服务响应:', paymentResult);

      // 检查富有支付服务响应
      if (!paymentResult.success) {
        throw new Error(`富有支付服务调用失败: ${paymentResult.error || '未知错误'}`);
      }

      // 🎯 第二步：API调用成功后，保存支付记录到数据库
      console.log('✅ 富有支付服务调用成功，保存数据库记录...');

      const order = await prisma.payment.create({
        data: {
          orderId: finalOrderId,
          amount: parseFloat(amount),
          description: description,
          status: 'pending',
          payment_method: 'fuiou', // 使用下划线命名
          createdAt: new Date(),
          type: 'payment',
          user: { connect: { id: userId } },
          fuiou_order_no: paymentResult.data.fuiouOrderNo, // 富有支付订单号
          fuiou_transaction_id: paymentResult.data.transactionId // 富有支付交易号
        }
      });

      console.log('✅ 数据库记录创建成功:', order.id);

      // 🎯 第三步：返回二维码给前端
      const qrCode = paymentResult.data.qrCode;

      res.json({
        success: true,
        message: '富有支付订单创建成功',
        data: {
          orderId: finalOrderId,
          qrCode: qrCode,
          amount: amount,
          paymentId: order.id
        }
      });

    } catch (error) {
      console.error('创建富有支付失败:', error);
      res.status(500).json({
        success: false,
        message: '创建富有支付失败',
        error: error.message
      });
    }
  }

  /**
   * 查询富有支付订单状态
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async queryFuiouPayment(req, res) {
    try {
      const { orderId } = req.params;
      const { orderType = 'ALIPAY' } = req.query;

      console.log(`🔍 查询富有支付状态 - 订单ID: ${orderId}`);

      // 查询订单信息
      const order = await prisma.payment.findUnique({
        where: { id: orderId }
      });

      if (!order) {
        return res.status(404).json({ error: '订单不存在' });
      }

      // 调用富有支付查询服务
      const queryResult = await FuiouService.queryOrder(orderId, orderType);

      if (queryResult.success) {
        const { data } = queryResult;

        // 如果支付成功，更新本地订单状态
        if (data.status === 'paid' && order.status !== 'paid') {
          await prisma.payment.update({
            where: { id: orderId },
            data: {
              status: 'paid',
              fuiou_transaction_id: data.transactionId,
              paid_at: new Date(data.paidTime)
            }
          });
        }

        return res.json({
          success: true,
          data: {
            orderId: data.orderId,
            status: data.status,
            amount: data.amount,
            paidTime: data.paidTime
          }
        });
      } else {
        return res.status(400).json({
          success: false,
          error: queryResult.error
        });
      }

    } catch (error) {
      console.error('查询富有支付状态失败:', error);
      return res.status(500).json({
        success: false,
        error: '查询支付状态失败'
      });
    }
  }

  /**
   * 处理富有支付回调通知
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async fuiouNotify(req, res) {
    try {
      console.log('📨 收到富有支付回调通知:', req.body);

      // 处理回调通知
      const notifyResult = await FuiouService.handleNotify(req.body);

      if (!notifyResult.success) {
        console.error('富有支付回调处理失败:', notifyResult.error);
        return res.status(400).send('FAIL');
      }

      const { data } = notifyResult;

      if (data.isPaid) {
        // 查询订单
        const order = await prisma.payment.findUnique({
          where: { id: data.orderId }
        });

        if (order && order.status !== 'paid') {
          // 更新订单状态
          await prisma.payment.update({
            where: { id: data.orderId },
            data: {
              status: 'paid',
              fuiou_transaction_id: data.transactionId,
              paid_at: new Date()
            }
          });

          console.log(`✅ 富有支付成功 - 订单ID: ${data.orderId}, 交易号: ${data.transactionId}`);
        }
      }

      // 返回成功响应 - 富有支付要求返回数字"1"
      return res.send('1');

    } catch (error) {
      console.error('富有支付回调处理异常:', error);
      return res.status(500).send('FAIL');
    }
  }
}

module.exports = new PaymentController();