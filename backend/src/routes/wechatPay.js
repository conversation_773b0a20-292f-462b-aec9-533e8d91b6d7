const express = require('express');
const router = express.Router();
const wechatPayService = require('../services/wechatPayService');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 创建微信支付订单
router.post('/create-order', async (req, res) => {
    try {
        const { amount, description, user_id } = req.body;

        if (!amount || amount <= 0) {
            return res.status(400).json({
                success: false,
                message: '支付金额无效'
            });
        }

        if (!wechatPayService.isAvailable()) {
            return res.status(503).json({
                success: false,
                message: '微信支付服务暂不可用'
            });
        }

        // 生成订单号
        const out_trade_no = `WX_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 创建支付订单
        const orderResult = await wechatPayService.createNativeOrder({
            out_trade_no: out_trade_no,
            amount: amount,
            description: description || '文档优化服务',
            notify_url: `${process.env.BASE_URL}/api/wechat-pay/notify`
        });

        if (orderResult.success) {
            // 保存订单到数据库
            await prisma.payment.create({
                data: {
                    orderId: out_trade_no,
                    amount: amount,
                    payment_method: 'wechat',
                    status: 'pending',
                    userId: user_id || 1,
                    description: description || '文档优化服务'
                }
            });

            res.json({
                success: true,
                data: {
                    out_trade_no: out_trade_no,
                    code_url: orderResult.code_url,
                    amount: amount
                }
            });
        } else {
            throw new Error('创建支付订单失败');
        }

    } catch (error) {
        console.error('创建微信支付订单失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '创建支付订单失败'
        });
    }
});

// 查询订单状态
router.get('/query-order/:out_trade_no', async (req, res) => {
    try {
        const { out_trade_no } = req.params;

        if (!wechatPayService.isAvailable()) {
            return res.status(503).json({
                success: false,
                message: '微信支付服务暂不可用'
            });
        }

        const result = await wechatPayService.queryOrder(out_trade_no);
        
        // 更新数据库订单状态
        if (result.success && result.trade_state === 'SUCCESS') {
            await prisma.payment.updateMany({
                where: { orderId: out_trade_no },
                data: { 
                    status: 'completed',
                    transactionId: result.transaction_id
                }
            });
        }

        res.json(result);

    } catch (error) {
        console.error('查询微信支付订单失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '查询订单失败'
        });
    }
});

// 微信支付回调
router.post('/notify', async (req, res) => {
    try {
        console.log('收到微信支付回调:', req.headers, req.body);

        if (!wechatPayService.isAvailable()) {
            return res.status(503).send('FAIL');
        }

        // 验证签名
        const isValid = wechatPayService.verifyNotification(req.headers, req.body);
        if (!isValid) {
            console.error('微信支付回调签名验证失败');
            return res.status(400).send('FAIL');
        }

        // 解密数据
        const decryptedData = wechatPayService.decryptNotification(req.body.resource);
        console.log('解密后的支付数据:', decryptedData);

        const { out_trade_no, trade_state, transaction_id } = decryptedData;

        if (trade_state === 'SUCCESS') {
            // 更新订单状态
            await prisma.payment.updateMany({
                where: { orderId: out_trade_no },
                data: { 
                    status: 'completed',
                    transactionId: transaction_id,
                    paidAt: new Date()
                }
            });

            console.log(`✅ 微信支付成功: ${out_trade_no}`);
        }

        res.send('SUCCESS');

    } catch (error) {
        console.error('处理微信支付回调失败:', error);
        res.status(500).send('FAIL');
    }
});

// 关闭订单
router.post('/close-order', async (req, res) => {
    try {
        const { out_trade_no } = req.body;

        if (!wechatPayService.isAvailable()) {
            return res.status(503).json({
                success: false,
                message: '微信支付服务暂不可用'
            });
        }

        const result = await wechatPayService.closeOrder(out_trade_no);
        
        if (result.success) {
            // 更新数据库订单状态
            await prisma.payment.updateMany({
                where: { orderId: out_trade_no },
                data: { status: 'cancelled' }
            });
        }

        res.json(result);

    } catch (error) {
        console.error('关闭微信支付订单失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '关闭订单失败'
        });
    }
});

module.exports = router;
