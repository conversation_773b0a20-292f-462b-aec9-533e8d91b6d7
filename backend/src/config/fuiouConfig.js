const crypto = require('crypto');

class FuiouConfig {
    constructor() {
        this.insCd = process.env.FUIOU_INS_CD;
        this.mchntCd = process.env.FUIOU_MCHNT_CD;
        this.mchntKey = process.env.FUIOU_MCHNT_KEY;
        this.orderPrefix = process.env.FUIOU_ORDER_PREFIX;
        this.notifyUrl = process.env.FUIOU_NOTIFY_URL;
        this.env = process.env.FUIOU_ENV || 'test';
        this.testUrl = process.env.FUIOU_TEST_URL || 'https://aipaytest.fuioupay.com';
        this.prodUrl = process.env.FUIOU_PROD_URL || 'https://aipay-cloud.fuioupay.com';

        // 验证必要配置
        this.validateConfig();
    }

    validateConfig() {
        const requiredFields = ['insCd', 'mchntCd', 'mchntKey', 'orderPrefix', 'notifyUrl'];
        const missingFields = requiredFields.filter(field => !this[field]);

        if (missingFields.length > 0) {
            throw new Error(`富有支付配置缺失: ${missingFields.join(', ')}`);
        }
    }

    // 获取API基础URL
    getApiBaseUrl() {
        if (this.env === 'prod') {
            return this.prodUrl;
        }
        return this.testUrl;
    }

    // 获取接口地址
    getApiUrl(endpoint) {
        const baseUrl = this.getApiBaseUrl();
        const endpoints = {
            preCreate: '/aggregatePay/preCreate',           // 统一下单
            micropay: '/aggregatePay/micropay',             // 条码支付
            commonQuery: '/aggregatePay/commonQuery',       // 订单查询
            commonRefund: '/aggregatePay/commonRefund',     // 退款申请
            wxPreCreate: '/aggregatePay/wxPreCreate',       // 公众号/服务窗统一下单
            closeOrder: '/aggregatePay/closeOrder',         // 关闭订单
            cancelOrder: '/aggregatePay/cancelOrder',       // 撤销订单
            refundQuery: '/aggregatePay/refundQuery'        // 退款查询
        };
        return baseUrl + (endpoints[endpoint] || endpoint);
    }

    // 获取基础请求参数
    getBaseParams() {
        return {
            version: '1.0',
            mchnt_cd: this.mchntCd,
            random_str: this.generateRandomString(32),
            term_id: this.generateRandomString(8) // 随机8字节数字字母组合
        };
    }

    // 获取商户配置
    getMerchantConfig() {
        return {
            insCd: this.insCd,
            mchntCd: this.mchntCd,
            mchntKey: this.mchntKey,
            notifyUrl: this.notifyUrl
        };
    }

    // 生成随机字符串
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // 生成商户订单号 - 符合富有支付规则
    generateMerchantOrderNo() {
        const prefix = this.orderPrefix || '18927'; // 5位商户号编码
        const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
        const randomNum = Math.random().toString().slice(2, 10); // 8位随机数

        // 流水号规则：商户号编码（5位）+日期（8位）+随机数（8位）
        return `${prefix}${date}${randomNum}`;
    }

    // 获取当前时间戳（yyyyMMddHHmmss格式）
    getCurrentTimestamp() {
        const date = new Date();
        return date.getFullYear().toString() + 
               (date.getMonth() + 1).toString().padStart(2, '0') + 
               date.getDate().toString().padStart(2, '0') + 
               date.getHours().toString().padStart(2, '0') + 
               date.getMinutes().toString().padStart(2, '0') + 
               date.getSeconds().toString().padStart(2, '0');
    }

    // 获取客户端IP（用于term_ip字段）
    getClientIP(req) {
        return req.ip || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress || 
               (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
               '127.0.0.1';
    }
}

module.exports = new FuiouConfig();
